package com.endovas.cps.controller.web.platform.patient;

import com.endovas.cps.enums.SurgeryStageEnum;
import com.endovas.cps.pojo.fo.patient.MedicalHisEditFO;
import com.endovas.cps.pojo.fo.patient.PatientAddFO;
import com.endovas.cps.pojo.fo.patient.PatientEditFO;
import com.endovas.cps.pojo.fo.patient.PatientSearchFO;
import com.endovas.cps.pojo.fo.resource.NormalFileAdditionalInfoFO;
import com.endovas.cps.pojo.fo.resource.NormalFileEditFO;
import com.endovas.cps.pojo.fo.resource.dicom.DicomAdditionalInfoFO;
import com.endovas.cps.pojo.vo.patient.PatientListVO;
import com.endovas.cps.pojo.vo.patient.PatientSelectVO;
import com.endovas.cps.pojo.vo.platform.hospital.HospitalSelectVO;
import com.endovas.cps.pojo.vo.stage.PostOPListVO;
import com.endovas.cps.service.hospital.HospitalService;
import com.endovas.cps.service.measurement.MeasurementResultService;
import com.endovas.cps.service.patient.PatientService;
import com.endovas.cps.service.patient.PatientTrackService;
import com.endovas.cps.service.resource.DicomService;
import com.endovas.cps.service.resource.NormalFileService;
import com.endovas.cps.service.stage.SurgeryIntraopService;
import com.google.common.collect.Maps;
import io.daige.starter.common.Project;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.render.Render;
import io.daige.starter.common.render.RenderJson;
import io.daige.starter.common.security.CurrentUser;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.BizAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2023/12/2
 * Time: 12:43
 */
@Slf4j
@RestController
@Api(value = "患者管理", tags = "患者管理接口")
@RequestMapping(Project.PLATFORM + "/patient")
@RequiredArgsConstructor
public class PatientController {
    private final DicomService dicomService;
    private final PatientService patientService;
    private final PatientTrackService trackService;
    private final HospitalService hospitalService;

    private final NormalFileService normalFileService;
    private final SurgeryIntraopService surgeryIntraopService;
    private final MeasurementResultService  measurementResultService;


    @ApiOperation(value = "病患下拉列表", notes = "")
    @GetMapping(path = "/select")
    public BaseResult<List<PatientSelectVO>> patientSelect(@ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(patientService.select(loginUser));
    }

    @ApiOperation(value = "医院下拉列表", notes = "")
    @GetMapping(path = "/hospital/select")
    public BaseResult<List<HospitalSelectVO>> hospitalSelect() {
        return Render.success(hospitalService.select());
    }


    @ApiOperation(value = "患者列表", notes = "")
    @GetMapping(path = "/list")
    public BaseResult<PageVO<PatientListVO>> list(PatientSearchFO searchFO, PageFO pageFO,
                                                  @ApiIgnore @CurrentUser LoginUser loginUser) {
        return Render.success(patientService.list(searchFO, pageFO, loginUser));
    }

    @ApiOperation(value = "添加患者", notes = "")
    @PostMapping(path = "/add")
    public String add(@Validated PatientAddFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        String patientId = patientService.add(input, loginUser);
        Map<String, String> result = Maps.newHashMap();
        result.put("patientId", patientId);
        return RenderJson.success(result);
    }

    @ApiOperation(value = "编辑患者", notes = "")
    @PostMapping(path = "/edit")
    public String edit(@Validated PatientEditFO input) {
        patientService.edit(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "编辑病史", notes = "")
    @PostMapping(path = "/editHis")
    public String editHis(@Validated MedicalHisEditFO input) {
        patientService.editMedicalHis(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "患者详情", notes = "")
    @GetMapping(path = "/detail")
    public String detail(String id, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(id, "患者ID不能为空");
        return RenderJson.success(patientService.detail(id, loginUser));
    }

    @ApiOperation(value = "删除患者", notes = "")
    @PostMapping(path = "/del")
    public String del(String id) {
        BizAssert.notEmpty(id, "患者ID不能为空");
        patientService.del(id);
        return RenderJson.success();
    }

    @ApiOperation(value = "术前影像列表", notes = "")
    @GetMapping(path = "/preop/list")
    public String preopList(String patientId, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(patientId, "患者ID不能为空");
        return RenderJson.success(measurementResultService.listResultByPatientId(patientId,  loginUser));
    }

    @ApiOperation(value = "测量规划方案", notes = "")
    @GetMapping(path = "/measure/list")
    public String measure(String patientId, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(patientId, "患者ID不能为空");



        return RenderJson.success(dicomService.list(patientId, SurgeryStageEnum.PREOP, loginUser));
    }

    @ApiOperation(value = "术中影像列表", notes = "")
    @GetMapping(path = "/intraop/list")
    public String intraopList(String patientId) {
        BizAssert.notEmpty(patientId, "患者ID不能为空");
        return RenderJson.success(surgeryIntraopService.detail(patientId));
    }

    @ApiOperation(value = "术后影像列表", notes = "")
    @GetMapping(path = "/postop/list")
    public String postopList(String patientId, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(patientId, "患者ID不能为空");
        PostOPListVO result = new PostOPListVO();
        result.setDicoms(dicomService.list(patientId, SurgeryStageEnum.POSTOP, loginUser));
        result.setNormalFiles(normalFileService.list(patientId, loginUser));
        return RenderJson.success(result);
    }


    @ApiOperation(value = "dicom补充信息", notes = "")
    @PostMapping(path = "/dicom/addInfo")
    public String addDicomInfo(@Validated DicomAdditionalInfoFO input, @ApiIgnore @CurrentUser LoginUser loginUser) {
        dicomService.additionalInfo(input);
        return RenderJson.success();
    }

    @ApiOperation(value = "normalFile补充信息", notes = "")
    @PostMapping(path = "/normalFile/addInfo")
    public String addNormalFileInfo(@Validated NormalFileAdditionalInfoFO input,
                                    @ApiIgnore @CurrentUser LoginUser loginUser) {
        NormalFileEditFO params = new NormalFileEditFO();
        params.setId(input.getId());
        params.setRemark(input.getRemark());
        normalFileService.edit(params);
        return RenderJson.success();
    }


    @ApiOperation(value = "术后随访列表", notes = "")
    @GetMapping(path = "/track/list")
    public String trackList(String patientId, @ApiIgnore @CurrentUser LoginUser loginUser) {
        BizAssert.notEmpty(patientId, "患者ID不能为空");
        return RenderJson.success(trackService.listByPatientId(patientId, loginUser));
    }
}
