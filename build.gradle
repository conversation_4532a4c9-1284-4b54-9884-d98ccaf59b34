plugins {
    id 'org.springframework.boot' version '2.5.14' apply false
    id 'io.spring.dependency-management' version '1.0.9.RELEASE'
}

//禁止根项目一切行为（不影响模块）
tasks.forEach {
    it.enabled = false
}
/**
 * 所有项目公共资源  
 */
allprojects {
    apply plugin: 'java-library'
    apply plugin: 'idea'
    group = 'com.daige'
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
    def REPOSITORY_HOME = "http://maven.aliyun.com"
    def NEXUS_HOME = "http://repo.90dai.com:8081"

    //仓库
    repositories {
        maven {
            allowInsecureProtocol true
            url "${NEXUS_HOME}/repository/maven-central/"
        }

        maven {
            allowInsecureProtocol true
            url "${NEXUS_HOME}/repository/maven-releases/"
        }

        maven {
            allowInsecureProtocol true
            url "${REPOSITORY_HOME}/nexus/content/groups/public/"
        }
        mavenLocal()
        mavenCentral()
        maven { url 'https://repo.spring.io/snapshot' }
        maven { url 'https://repo.spring.io/milestone' }
    }
}
/**
 * 子项目通用配置
 */
subprojects {
    apply plugin: 'io.spring.dependency-management'
    def springBootVersion = '2.5.14'

    // java编译的时候缺省状态下会因为中文字符而失败
    [compileJava, compileTestJava, javadoc]*.options*.encoding = 'UTF-8'


    dependencies {
        compileOnly 'org.projectlombok:lombok'
        annotationProcessor 'org.projectlombok:lombok'
        testAnnotationProcessor 'org.projectlombok:lombok'
        annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
        implementation 'cn.hutool:hutool-all:5.8.6'
        implementation 'org.springframework.boot:spring-boot-starter-websocket'
        implementation 'org.springframework.integration:spring-integration-redis'
        implementation 'org.springframework.boot:spring-boot-starter-data-redis'
        implementation 'commons-lang:commons-lang:2.6'
        implementation 'io.daige.starter.component:swagger-spring-boot-starter:0.1.1'
        implementation 'io.daige.starter.component:redis-spring-boot-starter:0.1.2'
        implementation 'io.daige.starter.component:captcha-spring-boot-starter:0.1.1'
        implementation 'io.daige.starter.component:idGenerator-spring-boot-starter:0.1.1'
        implementation 'io.daige.starter.component:jpa-soft-delete-spring-boot-starter:0.1.1'
        implementation 'io.daige.starter.component:secureTransport-spring-boot-starter:0.1.2'

        implementation 'io.daige.starter:common-starter-bean:0.1.1'

        implementation 'net.javacrumbs.shedlock:shedlock-spring:4.20.0'
        implementation 'net.javacrumbs.shedlock:shedlock-provider-mongo:4.20.0'

        implementation 'com.alibaba:transmittable-thread-local:2.12.0'
        implementation 'org.hibernate.validator:hibernate-validator'
        implementation 'org.springframework.data:spring-data-mongodb:3.3.5'

        implementation 'com.github.axet:java-unrar:1.7.0-8'
        implementation 'net.sf.sevenzipjbinding:sevenzipjbinding:16.02-2.01'
        implementation 'net.sf.sevenzipjbinding:sevenzipjbinding-all-platforms:16.02-2.01'

        implementation 'org.dcm4che:dcm4che-core:5.33.0'

        implementation 'com.aliyun.oss:aliyun-sdk-oss:3.16.1'
        implementation 'org.apache.guacamole:guacamole-common:1.1.0'


        implementation 'org.apache.commons:commons-lang3:3.10'

        testImplementation 'org.springframework.boot:spring-boot-starter-test'


    }
    dependencyManagement {
        imports { mavenBom("org.springframework.boot:spring-boot-dependencies:${springBootVersion}") }
    }

    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }
    test {
        useJUnitPlatform()
    }
}
