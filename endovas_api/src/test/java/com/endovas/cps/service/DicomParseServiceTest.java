package com.endovas.cps.service;

import com.endovas.cps.Application;
import com.endovas.cps.entity.attachment.Attachment;
import com.endovas.cps.service.resource.DicomParseService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.util.concurrent.ExecutionException;

/**
 * @author: wk
 * @Date: 2024/11/8
 * @Time: 17:26
 */
@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//配置启动类
public class DicomParseServiceTest {

    @Autowired
    private DicomParseService dicomParseService;

    @Test
    public void test() {
        try {
            String descDir = "/Users/<USER>/file/tempfile/";
            Attachment att = new Attachment();
            att.setId("123");
            att.setType("DICOM");

            dicomParseService.process(100L,descDir, att);
        } catch (ExecutionException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
