package com.endovas.cps.pojo.vo.measurement;

import com.endovas.cps.pojo.vo.AttachmentVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2025/7/30
 * Time: 13:52
 */
@Getter
@Setter
public class MeasurementTaskResultByPatientIdVO {
    @ApiModelProperty(value = "创建人")
    private String creator;
    @ApiModelProperty(value = "规划日期")
    private LocalDateTime taskCompletedTime;

    @ApiModelProperty(value = "手术阶段")
    private String surgeryStage;
    @ApiModelProperty(value = "来源类型")
    private String sourceType;


    @ApiModelProperty(value = "入路区")
    private String approachZone;
    @ApiModelProperty(value = "器械类型")
    private String instrumentType;
    @ApiModelProperty(value = "髂内动脉闭塞")
    private String iia;
    @ApiModelProperty(value = "评论")
    private String comment;

    @ApiModelProperty(value = "预览图")
    private AttachmentVO preview;
    @ApiModelProperty(value = "规划报告")
    private AttachmentVO report;


}
