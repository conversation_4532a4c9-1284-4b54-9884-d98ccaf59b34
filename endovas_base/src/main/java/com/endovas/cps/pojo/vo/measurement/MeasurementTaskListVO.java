package com.endovas.cps.pojo.vo.measurement;

import com.endovas.cps.entity.measurement.MeasurementTask;
import io.daige.starter.common.javabean.BeanConvert;
import io.daige.starter.common.pojo.vo.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 13:58
 */
@Getter
@Setter
public class MeasurementTaskListVO extends BaseVO implements BeanConvert<MeasurementTaskListVO, MeasurementTask> {
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "任务id")
    private String measurementTaskId;
    @ApiModelProperty(value = "疾病类型")
    private String SurgeryTypeName;
    @ApiModelProperty(value = "影像类型")
    private String modality;
    @ApiModelProperty(value = "发起人")
    private String creator;
    @ApiModelProperty(value = "发起人类型")
    private String creatorType;
    @ApiModelProperty(value = "领取人")
    private String measurer;
    @ApiModelProperty(value = "领取人类型")
    private String measurerType;
    @ApiModelProperty(value = "影像id")
    private String dicomId;
    @ApiModelProperty(value = "影像名称")
    private String dicomName;
    @ApiModelProperty(value = "影像文件内容日期")
    private String dicomContentDate;
    @ApiModelProperty(value = "医院名称")
    private String hospitalName;
    @ApiModelProperty(value = "患者id")
    private String patientId;
    @ApiModelProperty(value = "患者姓名")
    private String patientName;
    @ApiModelProperty(value = "任务状态")
    private String taskStatus;
    @ApiModelProperty(value = "任务测量环境准备状态")
    private String envPrepareStatus;
    @ApiModelProperty(value = "测量结果状态")
    private String resultSyncStatus;
    @ApiModelProperty(value = "任务类型")
    private String dataRangeType;
    @ApiModelProperty(value = "任务完成时间")
    private LocalDateTime taskCompletedTime;

    @ApiModelProperty(value = "是否是本人领取")
    private Boolean isSelf;

}
