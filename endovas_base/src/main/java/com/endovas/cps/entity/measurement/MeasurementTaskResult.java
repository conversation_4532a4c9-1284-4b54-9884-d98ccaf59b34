package com.endovas.cps.entity.measurement;

import com.baomidou.mybatisplus.annotation.TableName;
import io.daige.starter.common.database.mysql.MysqlBase;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 测量任务结果（记录需要测量的任务内容）
 *
 * @author: wk
 * @Date: 2024/11/1
 * @Time: 19:16
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "t_measurement_task_result")
@TableName("t_measurement_task_result")
public class MeasurementTaskResult extends MysqlBase {

    private String measurementId; //  测量任务ID
    private String result;
    private String sourceType; // MeasureSourceTypeEnum.UPLOAD  MeasureSourceTypeEnum.LOCAL
    private String resultFileName;
    private String resultFileHash;


    @Column(columnDefinition = "varchar(50) COMMENT '入路区'")
    private String approachZone;
    @Column(columnDefinition = "varchar(50) COMMENT '器械类型'")
    private String instrumentType;
    @Column(columnDefinition = "varchar(200) COMMENT '髂内动脉闭塞'")
    private String iia;
    @Column(columnDefinition = "text COMMENT '评论'")
    private String comment;

}
