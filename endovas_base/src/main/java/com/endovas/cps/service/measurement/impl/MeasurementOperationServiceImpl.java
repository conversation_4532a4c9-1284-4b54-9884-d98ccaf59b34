package com.endovas.cps.service.measurement.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GetObjectRequest;
import com.endovas.cps.config.FileSyncConfig;
import com.endovas.cps.config.properties.AliOSSProperties;
import com.endovas.cps.dao.PlatformUserDAO;
import com.endovas.cps.dao.measurement.MeasurementOperationDAO;
import com.endovas.cps.dao.measurement.MeasurementTaskDAO;
import com.endovas.cps.dao.platform.EquipmentDAO;
import com.endovas.cps.dao.resource.DicomDAO;
import com.endovas.cps.entity.measurement.MeasurementOperationRecord;
import com.endovas.cps.entity.measurement.MeasurementTask;
import com.endovas.cps.entity.platform.Equipment;
import com.endovas.cps.entity.resource.Dicom;
import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.enums.DicomStatusEnum;
import com.endovas.cps.enums.EquipmentStatusEnum;
import com.endovas.cps.enums.MeasurementStatusEnum;
import com.endovas.cps.pojo.dto.CreatorTypeDTO;
import com.endovas.cps.pojo.fo.measurement.MeasurementOperationParamFO;
import com.endovas.cps.pojo.vo.AttachmentVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationAllocVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationDetailVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationListVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementOperationPrepareVO;
import com.endovas.cps.pojo.vo.platform.equip.EquipmentDetailVO;
import com.endovas.cps.service.attachment.AttachmentService;
import com.endovas.cps.service.attachment.FileService;
import com.endovas.cps.service.equipment.EquipmentService;
import com.endovas.cps.service.measurement.MeasurementOperationService;
import com.endovas.cps.service.measurement.MeasurementTaskService;
import com.endovas.cps.service.organization.OrganizationService;
import com.endovas.cps.service.resource.DicomService;
import com.endovas.cps.service.user.CreatorTypeService;
import com.endovas.cps.util.HttpRequestUtils;
import com.endovas.cps.util.SNUtils;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.render.BaseResult;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wk
 * @Date: 2024/11/18
 * @Time: 16:54
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeasurementOperationServiceImpl implements MeasurementOperationService {

    private final EquipmentService equipmentService;
    private final EquipmentDAO equipmentDAO;
    private final DicomService dicomService;
    private final DicomDAO dicomDAO;
    private final MeasurementTaskDAO measurementTaskDAO;
    private final MeasurementOperationDAO measurementOperationDAO;
    private final MeasurementTaskService measurementTaskService;
    private final PlatformUserDAO platformUserDAO;
    private final OrganizationService organizationService;
    private final AttachmentService attachmentService;
    private final CreatorTypeService creatorTypeService;
    private final OSSClient ossClient;
    private final FileService fileService;
    private final FileSyncConfig fileSyncConfig;
    private final HttpRequestUtils httpRequestUtils;
    private final AliOSSProperties aliOSSProperties;
    private final RedisHelper redisHelper;


    /**
     * 尝试分配机器
     *
     * @param searchFO
     * @param loginUser
     * @return
     */
    @Override
    public MeasurementOperationAllocVO tryAllocateEquip(MeasurementOperationParamFO searchFO, LoginUser loginUser) {

        MeasurementOperationAllocVO result = new MeasurementOperationAllocVO();

        // 如果测量任务环境已经准备好，需要查找原来的设备
        String equipId = null;
        MeasurementTask task =
                measurementTaskDAO.findById(searchFO.getMeasurementId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        if (task.getEnvPrepareStatus().equals(MeasurementStatusEnum.ENVPREPARESTATUS_COMPLETED.getCode())) {
            MeasurementOperationRecord lastRecord =
                    measurementOperationDAO.findFirstByMeasurementIdOrderByCreateTimeDesc(searchFO.getMeasurementId());
            equipId = lastRecord.getEquipmentId();
        }
        log.info("tryAllocateEquip_原来的设备equipId:{}", equipId);

        // 分配设备
        EquipmentDetailVO equip = null;
        boolean isIdelEquip = false; // 是否是空闲设备
        if (StringUtils.isBlank(equipId)) {
            // 尝试获取空闲设备
            equip = equipmentService.allocateEquip(null, searchFO.getSoftName());
            // 如果正常分配没有获取到机器，则使用踢人的方式分配机器
            if (Objects.isNull(equip)) {
                // 分配一台正在使用的机器
                equip = equipmentService.allocateEquipKickOther(null, searchFO.getSoftName());
                if (Objects.isNull(equip)) {
                    throw new BusinessAssertException("当前无设备可分配，请联系管理员");
                }
            } else {
                isIdelEquip = true;
            }
            equipId = equip.getId();
        }
        result.setEquipId(equipId);
        log.info("tryAllocateEquip_分配后的设备equipId:{}", equipId);

        // 检查是否要踢人
        Boolean needKickOther = false;
        Equipment eqp = equipmentDAO.findById(equipId).orElseThrow(() -> new BusinessAssertException("未查询到设备"));
        if (eqp.getStatus().equals(EquipmentStatusEnum.INUSE.getCode()) && !isIdelEquip) {
            needKickOther = true;
        }
        if (!needKickOther) {
            log.info("tryAllocateEquip_有空闲设备:{}", needKickOther);
            result.setNeedKickOther(false);
            return result;
        }

        // 找到正在使用机器的人员
        MeasurementOperationRecord mo =
                measurementOperationDAO.findFirstByEquipmentIdAndIsUsingEquipIsTrueOrderByCreateTimeDesc(equipId);
        if (Objects.isNull(mo)) {
            result.setNeedKickOther(false);
            return result;
        }
        PlatformUser currentUser =
                platformUserDAO.findById(mo.getCreatorId()).orElseThrow(() -> new BusinessAssertException(
                        "未查询到使用设备的用户"));
        log.info("tryAllocateEquip_当前使用的操作记录:{},当前使用的用户:{},当前登录的用户:{}", mo.getId(), currentUser.getNickName(),
                loginUser.getNickName());
        result.setCurrentUser(currentUser.getNickName());
        result.setLastLoginTime(currentUser.getLastLoginTime());
        String orgPath = organizationService.getOrgNamePath(currentUser.getOrganizationId(), null);
        result.setCurrentUserOrg(orgPath);
        result.setNeedKickOther(true);

        return result;
    }

    /**
     * 查询当前机器使用人
     *
     * @param searchFO
     * @param loginUser
     * @return
     */
    @Override
    public MeasurementOperationAllocVO queryEquipCurrentUser(MeasurementOperationParamFO searchFO,
                                                             LoginUser loginUser) {
        // 查询出掉线的测量任务
        MeasurementOperationRecord opRecord =
                measurementOperationDAO.findById(searchFO.getMeasureOpRecordId()).orElseThrow(() -> new BusinessAssertException("未查询到当前的测量操作记录"));

        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            log.error("等待踢人的远程连接初始化成功后查询是谁", e);
        }
        // 根据设备id查询出占用的人员
        // MeasurementOperationRecord currentKickOpRecord = measurementOperationDAO
        // .findFirstByEquipmentIdAndIsUsingEquipIsTrueOrderByCreateTimeDesc();
        String userId = redisHelper.strGet(RedisCacheKeys.getEquipInuseUserIdKey(opRecord.getEquipmentId()));
        if (Objects.isNull(userId)) {
            throw new BusinessAssertException("远程连接已断开");
        }

        MeasurementOperationAllocVO result = new MeasurementOperationAllocVO();
        PlatformUser currentUser = platformUserDAO.findById(userId).orElseThrow(() -> new BusinessAssertException(
                "未查询到使用设备的用户"));
        result.setCurrentUser(currentUser.getNickName());
        result.setLastLoginTime(currentUser.getLastLoginTime());
        String orgPath = organizationService.getOrgNamePath(currentUser.getOrganizationId(), null);
        result.setCurrentUserOrg(orgPath);

        return result;
    }


    @Override
    public MeasurementOperationPrepareVO prepare(MeasurementOperationParamFO input, LoginUser loginUser) {
        /**
         * 查询测量任务，如果没完成初始化，则开始初始化
         */
        // 查询出测量任务
        MeasurementTask taskPO =
                measurementTaskDAO.findById(input.getMeasurementId()).orElseThrow(() -> new RuntimeException(
                        "未查询到测量任务"));

        // 生成连接token令牌，防止刷新页面自动重新连接
        String token = IdUtil.fastSimpleUUID();

        // 判断环境是否准备好
        if (taskPO.getEnvPrepareStatus().equals(MeasurementStatusEnum.ENVPREPARESTATUS_COMPLETED.getCode())) {
            // 记录操作信息
            MeasurementOperationRecord opRecord = new MeasurementOperationRecord();
            opRecord.setMeasurementId(taskPO.getId());
            opRecord.setDicomId(taskPO.getDicomId());
            opRecord.setEquipmentId(input.getEquipId());
            opRecord.setCreatorBelong(loginUser.getBelong().getCode());
            opRecord.setIsFinished(false);
            opRecord.setIsUsingEquip(false);
            measurementOperationDAO.save(opRecord);

            // 返回结果
            redisHelper.strSet(RedisCacheKeys.getMeasurementOperationTokenKey(opRecord.getId()), token);
            MeasurementOperationPrepareVO result = new MeasurementOperationPrepareVO();
            result.setUserId(loginUser.getId());
            result.setMeasureOpRecordId(opRecord.getId());
            result.setMeasureOpToken(token);
            return result;

        } else {

            Equipment equip =
                    equipmentDAO.findById(input.getEquipId()).orElseThrow(() -> new BusinessAssertException(
                            "未查询到设备信息"));

            // 更新测量任务环境准备状态
            measurementTaskService.measureOperationUpdateEnvPrepareStatus(taskPO.getId(),
                    MeasurementStatusEnum.ENVPREPARESTATUS_ALLOCATE_DEVICE.getCode(), loginUser);

            // 记录操作信息
            MeasurementOperationRecord opRecord = new MeasurementOperationRecord();
            opRecord.setMeasurementId(taskPO.getId());
            opRecord.setDicomId(taskPO.getDicomId());
            opRecord.setEquipmentId(equip.getId());
            opRecord.setCreatorBelong(loginUser.getBelong().getCode());
            opRecord.setIsFinished(false);
            opRecord.setIsUsingEquip(false);
            measurementOperationDAO.save(opRecord);
            // 更新测量任务环境准备状态
            measurementTaskService.measureOperationUpdateEnvPrepareStatus(taskPO.getId(),
                    MeasurementStatusEnum.ENVPREPARESTATUS_INIT.getCode(), loginUser);

            // 上传影像资料
            // 更新测量任务环境准备状态
            measurementTaskService.measureOperationUpdateEnvPrepareStatus(taskPO.getId(),
                    MeasurementStatusEnum.RESULTSYNCSTATUS_UPLOAD_DATA.getCode(), loginUser);
            uploadData(taskPO.getDicomId(), equip.getHostname(), equip.getUsername(),
                    taskPO.getMeasurementTaskId() + "_" + taskPO.getPatientName());
            // 更新测量任务环境准备状态
            measurementTaskService.measureOperationUpdateEnvPrepareStatus(taskPO.getId(),
                    MeasurementStatusEnum.ENVPREPARESTATUS_COMPLETED.getCode(), loginUser);

            // 返回结果
            redisHelper.strSet(RedisCacheKeys.getMeasurementOperationTokenKey(opRecord.getId()), token);
            MeasurementOperationPrepareVO result = new MeasurementOperationPrepareVO();
            result.setUserId(loginUser.getId());
            result.setMeasureOpRecordId(opRecord.getId());
            result.setMeasureOpToken(token);
            return result;
        }

    }


    /**
     * 查询测量操作列表
     *
     * @param searchFO
     * @param loginUser
     * @return
     */
    @Override
    public List<MeasurementOperationListVO> listRecord(MeasurementOperationParamFO searchFO, LoginUser loginUser) {
        List<MeasurementOperationRecord> poList =
                measurementOperationDAO.findByMeasurementIdAndIsFinishedIsTrueOrderByCreateTimeDesc(searchFO.getMeasurementId());

        if (CollectionUtil.isEmpty(poList)) {
            return new ArrayList<>();
        }

        List<MeasurementOperationListVO> resultList = poList.stream().map(po -> {
            MeasurementOperationListVO vo = new MeasurementOperationListVO();
            vo.convertFrom(po);

            // 发起人
            CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(),
                    BelongEnum.valueOf(po.getCreatorBelong()), po.getCreatorId());
            vo.setMeasurer(creatorTypeDTO.getCreator());
            vo.setMeasurerType(creatorTypeDTO.getCreatorType());

            return vo;
        }).collect(Collectors.toList());

        return resultList;
    }

    /**
     * 获取测量记录
     *
     * @param id
     * @return
     */
    @Override
    public MeasurementOperationDetailVO getRecord(String id) {
        MeasurementOperationRecord record =
                measurementOperationDAO.findById(id).orElseThrow(() -> new BusinessAssertException("未查询到操作记录信息"));
        MeasurementOperationDetailVO vo = new MeasurementOperationDetailVO().convertFrom(record);
        return vo;
    }

    private boolean isFileBeingWriting(File dicomFile) {

        boolean isLocked = false;
        FileChannel channel = null;
        FileLock lock = null;

        try {
            // 打开文件通道
            channel = new RandomAccessFile(dicomFile, "rw").getChannel();
            // 尝试获取文件锁
            lock = channel.tryLock();

            if (lock == null) {
                // 如果无法获取锁，文件正在被写入
                isLocked = true;
            }
        } catch (Exception e) {
            log.error("检测文件锁异常", e);
        } finally {
            try {
                if (lock != null) {
                    lock.release();
                }
                if (channel != null) {
                    channel.close();
                }
            } catch (Exception e) {
                log.error("释放文件锁异常", e);
            }
        }

        return isLocked;
    }

    /**
     * 上传文件至测量设备特定位置
     *
     * @param dicomId
     * @param targetDeviceIp
     */
    public String uploadData(String dicomId, String targetDeviceIp, String targetDeviceUsername, String taskId) {

        // 准备文件
        AttachmentVO attachment = attachmentService.getByTargetIdAndType(dicomId, AttachmentTypeEnum.DICOM);
        String downloadTmpDir = fileService.genLocalTmpDownLoadFilePath(aliOSSProperties.getTmpDir(),
                attachment.getId(), attachment.getType());
        String downloadPath = downloadTmpDir + attachment.getName();

        Dicom dicomPO = dicomDAO.findById(dicomId).orElseThrow(() -> new BusinessAssertException("未查询到影像文件"));

        File dicomFile = new File(downloadPath);
        try {
            // 等待dicom上传的文件下载完成，避免上传文件时文件不存在导致上传失败
            int times = 30;
            while (true) {
                if (DicomStatusEnum.TEMPFILE_READY.getCode().equals(dicomPO.getTempFileStatus())) {
                    break;
                } else {
                    Thread.sleep(2000);
                }
                times--;
                if (times <= 0) {
                    break;
                }
            }

            /*// 检测文件是否被写入
            int ts = 15;
            while(true) {
                if (!isFileBeingWriting(dicomFile)){
                    break;
                } else {
                    Thread.sleep(2000);
                }
                ts--;
                if(ts<=0){
                    break;
                }
            }*/
        } catch (InterruptedException e) {
            log.error("等待下载完成失败", e);
        }

        if (!dicomFile.exists()) {
            // 下载文件
            ossClient.getObject(new GetObjectRequest(aliOSSProperties.getBucketName(), attachment.getUrl()),
                    new File(downloadPath));
            dicomService.updateTempFileStatusToFinish(dicomId);
        }

        // 准备其他参数
        String serialNo = SNUtils.getCrccfcSn();
        Map<String, String> param = new HashMap<>();
        param.put("serialNo", serialNo);
        param.put("serviceCode", "measurementOperationUpload");
        param.put("userName", targetDeviceUsername);
        param.put("path", taskId);
        param.put("fileName", attachment.getName());
        // 组装地址
        StringBuilder sb = new StringBuilder("http://");
        sb.append(targetDeviceIp);
        sb.append(":");
        sb.append(fileSyncConfig.getServicePort());
        if (!fileSyncConfig.getApiUrl().startsWith("/")) {
            sb.append("/");
        }
        sb.append(fileSyncConfig.getApiUrl());
        String url = sb.toString();


        // 上传文件
        LocalDateTime startTime = LocalDateTime.now();
        log.info("uploadData_request_serialNo:{}  uploadData_request_startTime:{}", "", serialNo,
                startTime.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
        log.info("uploadData_url:{}", url);

        String resp = httpRequestUtils.postFile(url, dicomFile, param);
        if (StringUtils.isBlank(resp)) {
            return null;
        }

        LocalDateTime endTime = LocalDateTime.now();
        log.info("uploadData_request_serialNo:{} uploadData_response_data:{} uploadData_response_endTime:{}",
                serialNo, resp, endTime.format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));

        BaseResult<String> data = JSONUtil.toBean(resp, new TypeReference<BaseResult<String>>() {
        }.getType(), true);
        String resData = (String) data.getResponse();
        return resData; //JSONUtil.toBean(resData, EncryptDataRes.class);
    }

}
