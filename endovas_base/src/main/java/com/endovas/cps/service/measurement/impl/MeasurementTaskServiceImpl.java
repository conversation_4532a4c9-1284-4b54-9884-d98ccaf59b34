package com.endovas.cps.service.measurement.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.endovas.cps.constant.MeasurementTaskRecordConst;
import com.endovas.cps.dao.PlatformUserDAO;
import com.endovas.cps.dao.hospital.HospitalDAO;
import com.endovas.cps.dao.measurement.MeasurementTaskDAO;
import com.endovas.cps.dao.patient.PatientDAO;
import com.endovas.cps.dao.resource.DicomDAO;
import com.endovas.cps.dao.surgery.SurgeryTypeDAO;
import com.endovas.cps.entity.measurement.MeasurementTask;
import com.endovas.cps.entity.resource.Dicom;
import com.endovas.cps.entity.user.PlatformUser;
import com.endovas.cps.enums.MeasurementStatusEnum;
import com.endovas.cps.pojo.dto.CreatorTypeDTO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskAddFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskDetailFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskEditFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskModifyFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskSearchFO;
import com.endovas.cps.pojo.fo.measurement.MeasurementTaskSearchSelfFO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskInfoVO;
import com.endovas.cps.pojo.vo.measurement.MeasurementTaskListVO;
import com.endovas.cps.service.access.DataAccessService;
import com.endovas.cps.service.measurement.MeasurementTaskRecordService;
import com.endovas.cps.service.measurement.MeasurementTaskService;
import com.endovas.cps.service.surgery.SurgeryTypeService;
import com.endovas.cps.service.user.CreatorTypeService;
import com.endovas.cps.util.NameHiddenUtil;
import io.daige.starter.common.cache.RedisCacheKeys;
import io.daige.starter.common.enums.BelongEnum;
import io.daige.starter.common.exception.BusinessAssertException;
import io.daige.starter.common.pojo.fo.PageFO;
import io.daige.starter.common.pojo.vo.PageVO;
import io.daige.starter.common.security.LoginUser;
import io.daige.starter.common.utils.DateUtil;
import io.daige.starter.common.utils.PageUtil;
import io.daige.starter.component.redis.service.RedisHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wk
 * @Date: 2024/11/4
 * @Time: 10:40
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeasurementTaskServiceImpl implements MeasurementTaskService {

    private final MeasurementTaskDAO measurementTaskDAO;
    private final RedisHelper redisHelper;
    private final DicomDAO dicomDAO;
    private final SurgeryTypeDAO surgeryTypeDAO;
    private final SurgeryTypeService surgeryTypeService;

    private final PlatformUserDAO platformUserDAO;
    private final HospitalDAO hospitalDAO;
    private final PatientDAO patientDAO;
    private final DataAccessService dataAccessService;

    private final CreatorTypeService creatorTypeService;
    private final MeasurementTaskRecordService measurementTaskRecordService;


    /**
     * 获取自己测量任务池子（隔离非必要参数）
     *
     * @param searchFO
     * @param page
     * @return
     */
    @Override
    public PageVO<MeasurementTaskListVO> listSelfMissionPool(MeasurementTaskSearchSelfFO searchFO, PageFO page, LoginUser loginUser) {
        MeasurementTaskSearchFO fo = new MeasurementTaskSearchFO();
        BeanUtil.copyProperties(searchFO, fo);
        fo.setMeasurer(loginUser.getNickName());
        return listMissionPool(fo, page, loginUser);
    }

    @Override
    public PageVO<MeasurementTaskListVO> listMissionPool(MeasurementTaskSearchFO searchFO, PageFO page, LoginUser loginUser) {
        Specification<MeasurementTask> specification = (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = dataAccessService.filterDataAuth(root, criteriaBuilder, loginUser);

            // 任务id
            if (StrUtil.isNotBlank(searchFO.getMeasurementTaskId())) {
                Predicate p1 = criteriaBuilder.equal(root.get(MeasurementTask.MEASUREMENT_TASK_ID), searchFO.getMeasurementTaskId());
                list.add(p1);
            }

            // 疾病类型id
            if (StrUtil.isNotBlank(searchFO.getSurgeryTypeId())) {
                List<String> allSurgeryTypeIds = surgeryTypeService.parentAndChildren(searchFO.getSurgeryTypeId());
                Predicate p1 = criteriaBuilder.in(root.get(MeasurementTask.SURGERY_TYPE_ID)).value(allSurgeryTypeIds);
                list.add(p1);
            }

            // 发起人
            if (StrUtil.isNotBlank(searchFO.getCreator())) {

                List<PlatformUser> userList = platformUserDAO.findByNickNameContaining(searchFO.getCreator());
                if (CollectionUtil.isNotEmpty(userList)) {
                    List<String> userIdList = userList.stream().map(PlatformUser::getId).collect(Collectors.toList());
                    Predicate p1 = criteriaBuilder.in(root.get(MeasurementTask.CREATOR_ID)).value(userIdList);
                    list.add(p1);
                } else {
                    Predicate p1 = criteriaBuilder.equal(root.get(MeasurementTask.CREATOR_ID), searchFO.getCreator());
                    list.add(p1);
                }
            }

            // 领取人
            if (StrUtil.isNotBlank(searchFO.getMeasurer())) {

                List<PlatformUser> userList = platformUserDAO.findByNickNameContaining(searchFO.getMeasurer());
                if (CollectionUtil.isNotEmpty(userList)) {
                    List<String> userIdList = userList.stream().map(PlatformUser::getId).collect(Collectors.toList());
                    Predicate p1 = criteriaBuilder.in(root.get(MeasurementTask.MEASURER_ID)).value(userIdList);
                    list.add(p1);
                } else {
                    Predicate p1 = criteriaBuilder.equal(root.get(MeasurementTask.MEASURER_ID), searchFO.getMeasurer());
                    list.add(p1);
                }
            }

            // 任务状态
            if (StrUtil.isNotBlank(searchFO.getTaskStatus())) {
                Predicate p1 = criteriaBuilder.equal(root.get(MeasurementTask.TASK_STATUS), searchFO.getTaskStatus());
                list.add(p1);
            }

            // 医院名称
            if (StrUtil.isNotBlank(searchFO.getHospitalId())) {
                Predicate p1 = criteriaBuilder.equal(root.get(MeasurementTask.HOSPITAL_ID), searchFO.getHospitalId());
                list.add(p1);
            }

            // 患者姓名
            if (StrUtil.isNotBlank(searchFO.getPatientName())) {
                Predicate p1 = criteriaBuilder.like(root.get(MeasurementTask.PATIENT_NAME), "%" + searchFO.getPatientName() + "%");
                list.add(p1);
            }

            // 患者id
            if (StrUtil.isNotBlank(searchFO.getPatientId())) {
                Predicate p1 = criteriaBuilder.equal(root.get(MeasurementTask.PATIENT_ID), searchFO.getPatientId());
                list.add(p1);
            }

            // 影像名称
            if (StrUtil.isNotBlank(searchFO.getDicomName())) {
                Predicate p1 = criteriaBuilder.equal(root.get(MeasurementTask.DICOM_NAME), searchFO.getDicomName());
                list.add(p1);
            }

            // 创建时间
            if (StrUtil.isNotBlank(searchFO.getCreateStartDate()) && StrUtil.isNotBlank(searchFO.getCreateEndDate())) {
                LocalDateTime startDate = DateUtil.getStartTime(searchFO.getCreateStartDate());
                LocalDateTime endDate = DateUtil.getEndTime(searchFO.getCreateEndDate());
                Predicate p1 = criteriaBuilder.between(root.get(MeasurementTask.CREATE_TIME), startDate, endDate);
                list.add(p1);
            }

            return criteriaBuilder.and(list.toArray(new Predicate[list.size()]));
        };


        Page<MeasurementTaskListVO> list = measurementTaskDAO.findAll(specification, PageUtil.initJPAPage(page)).map(x -> {
            MeasurementTaskListVO one = new MeasurementTaskListVO();
            one.convertFrom(x);
            one.setPatientName(NameHiddenUtil.hidden(x.getPatientName()));

            // 发起人
            CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(x.getCreatorBelong()), x.getCreatorId());
            one.setCreator(creatorTypeDTO.getCreator());
            one.setCreatorType(creatorTypeDTO.getCreatorType());
            if (StringUtils.isNotBlank(x.getMeasurerId())) {
                // 领取人
                CreatorTypeDTO measurerTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(x.getMeasurerBelong()), x.getMeasurerId());
                one.setMeasurer(measurerTypeDTO.getCreator());
                one.setMeasurerType(measurerTypeDTO.getCreatorType());
            }

            hospitalDAO.findById(x.getHospitalId()).ifPresent(h -> one.setHospitalName(h.getName()));

            // 手术类型名称
            if (StrUtil.isNotEmpty(x.getSurgeryTypeId())) {
                surgeryTypeDAO.findById(x.getSurgeryTypeId()).ifPresent(s -> one.setSurgeryTypeName(s.getName()));
            }

            // 是否是本人领取(方便用于前端按钮显示判断)
            if (loginUser.getId().equals(x.getMeasurerId())) {
                one.setIsSelf(true);
            } else {
                one.setIsSelf(false);
            }

            return one;
        });
        return PageUtil.convert(list);
    }



    @Override
    public MeasurementTaskInfoVO detail(MeasurementTaskDetailFO detailFO, LoginUser loginUser) {

        MeasurementTask po = measurementTaskDAO.findById(detailFO.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));

        MeasurementTaskInfoVO vo = new MeasurementTaskInfoVO();
        vo.convertFrom(po);
        // 发起人
        CreatorTypeDTO creatorTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(po.getCreatorBelong()), po.getCreatorId());
        vo.setCreator(creatorTypeDTO.getCreator());
        vo.setCreatorType(creatorTypeDTO.getCreatorType());

        // 领取人
        if (StringUtils.isNotBlank(po.getMeasurerId())) {
            CreatorTypeDTO measurerTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(po.getMeasurerBelong()), po.getMeasurerId());
            vo.setMeasurer(measurerTypeDTO.getCreator());
            vo.setMeasurerType(measurerTypeDTO.getCreatorType());
        }


        /**
         * 补充影像文件信息
         */
        Dicom dicomPO = dicomDAO.findById(po.getDicomId()).orElseThrow(() -> new BusinessAssertException("未查询到影像信息"));
        vo.setDicomName(dicomPO.getName());
        vo.setDicomUploadDateTime(dicomPO.getCreateTime());
        vo.setViewJsonUrl(dicomPO.getViewJsonUrl());
        // 影像文件上传人
        CreatorTypeDTO dicomUploaderTypeDTO = creatorTypeService.convert(loginUser.getBelong(), BelongEnum.valueOf(dicomPO.getCreatorBelong()), dicomPO.getCreatorId());
        vo.setDicomUploader(dicomUploaderTypeDTO.getCreator());
        vo.setDicomUploaderType(dicomUploaderTypeDTO.getCreatorType());

        // 手术类型名称
        if (StrUtil.isNotEmpty(po.getSurgeryTypeId())) {
            surgeryTypeDAO.findById(po.getSurgeryTypeId()).ifPresent(s -> vo.setSurgeryTypeName(s.getNameWithParent()));
        }

        return vo;
    }


    /**
     * 添加测量任务
     *
     * @param input
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MeasurementTaskInfoVO add(MeasurementTaskAddFO input, LoginUser loginUser) {
        // 生成id
        MeasurementTask taskPO = new MeasurementTask();
        taskPO.setMeasurementTaskId(getTaskId());
        // 设置状态
        taskPO.setTaskStatus(MeasurementStatusEnum.TASKSTATUS_UNCLAIMED.getCode());
        taskPO.setResultSyncStatus(MeasurementStatusEnum.RESULTSYNCSTATUS_NOTSTARTED.getCode());
        taskPO.setEnvPrepareStatus(MeasurementStatusEnum.ENVPREPARESTATUS_NOTSTARTED.getCode());
        taskPO.setMedAgentId(loginUser.getMedAgentId());
        // 设置默认参数
        taskPO.setCreatorBelong(loginUser.getBelong().getCode());
        // 保存参数信息
        input.convertTo(taskPO);
        taskPO.setMeasurerId(""); // 先保存未领取的状态，如果有指派人，后续步骤处理
        /**
         * 查询关联信息
         */
        // 影像类型
        Dicom dicomPO = dicomDAO.findById(taskPO.getDicomId()).orElseThrow(() -> new BusinessAssertException("未查询到影像信息"));
        dicomPO.setPatientId(input.getPatientId());
        dicomDAO.save(dicomPO);

        taskPO.setModality(dicomPO.getModality());
        taskPO.setDicomName(dicomPO.getName());
        taskPO.setDicomContentDate(dicomPO.getContentDate());
        // 患者姓名
        patientDAO.findById(taskPO.getPatientId()).ifPresent(p -> taskPO.setPatientName(p.getName()));
        // 保存记录
        measurementTaskDAO.save(taskPO);
        // 保存日志
        measurementTaskRecordService.add(null, taskPO, MeasurementTaskRecordConst.CREATE, taskPO.getCreatorId(), taskPO.getCreatorBelong());


        // 处理指派给自己或别人的逻辑
        String measurerId = null;
        if (input.getAssignToSelf()) {
            measurerId = loginUser.getId();
        } else if (StringUtils.isNotBlank(input.getMeasurerId())) {
            measurerId = input.getMeasurerId();
        }
        // 如果指定了领取人
        if (StringUtils.isNotBlank(measurerId)) {
            taskPO.setMeasurerId(measurerId);
            taskPO.setMeasurerBelong(loginUser.getBelong().getCode()); // 前提：当前登录用户只能查到本组织的归属

            MeasurementTask beforePO = new MeasurementTask();
            BeanUtil.copyProperties(taskPO, beforePO);
            taskPO.setTaskStatus(MeasurementStatusEnum.TASKSTATUS_ALREADYCLAIMED.getCode());
            measurementTaskDAO.save(taskPO);
            // 保存日志
            measurementTaskRecordService.add(beforePO, taskPO, MeasurementTaskRecordConst.COLLECT, measurerId, loginUser.getBelong().getCode());
        }

        MeasurementTaskInfoVO vo = new MeasurementTaskInfoVO();
        vo.convertFrom(taskPO);
        return vo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(MeasurementTaskEditFO input) {
        MeasurementTask po = measurementTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        po.setDataRangeType(input.getDataRangeType());

        measurementTaskDAO.save(po);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void collect(MeasurementTaskModifyFO input, LoginUser loginUser) {
        MeasurementTask po = measurementTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        MeasurementTask beforePO = new MeasurementTask();
        BeanUtil.copyProperties(po, beforePO);

        // 领取人信息更新
        po.setMeasurerId(loginUser.getId());
        po.setMeasurerBelong(loginUser.getBelong().getCode());

        // 状态更新
        po.setTaskStatus(MeasurementStatusEnum.TASKSTATUS_ALREADYCLAIMED.getCode());

        measurementTaskDAO.save(po);

        // 保存日志
        measurementTaskRecordService.add(beforePO, po, MeasurementTaskRecordConst.COLLECT, loginUser.getId(), loginUser.getBelong().getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void giveUp(MeasurementTaskModifyFO input, LoginUser loginUser) {
        MeasurementTask po = measurementTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        MeasurementTask beforePO = new MeasurementTask();
        BeanUtil.copyProperties(po, beforePO);

        // 领取人信息更新
        po.setMeasurerId("");
        po.setMeasurerBelong("");

        // 状态更新
        po.setTaskStatus(MeasurementStatusEnum.TASKSTATUS_UNCLAIMED.getCode());

        measurementTaskDAO.save(po);

        // 保存日志
        measurementTaskRecordService.add(beforePO, po, MeasurementTaskRecordConst.GIVE_UP, loginUser.getId(), loginUser.getBelong().getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finish(MeasurementTaskModifyFO input, LoginUser loginUser) {
        MeasurementTask po = measurementTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        MeasurementTask beforePO = new MeasurementTask();
        BeanUtil.copyProperties(po, beforePO);

        // 状态更新
        po.setTaskStatus(MeasurementStatusEnum.TASKSTATUS_COMPLETED.getCode());
        po.setTaskCompletedTime(LocalDateTime.now());
        po.setEnvPrepareStatus(MeasurementStatusEnum.ENVPREPARESTATUS_NOTSTARTED.getCode());
        measurementTaskDAO.save(po);

        // 保存日志
        measurementTaskRecordService.add(beforePO, po, MeasurementTaskRecordConst.COMPLETED, loginUser.getId(), loginUser.getBelong().getCode());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void close(MeasurementTaskModifyFO input, LoginUser loginUser) {
        MeasurementTask po = measurementTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        MeasurementTask beforePO = new MeasurementTask();
        BeanUtil.copyProperties(po, beforePO);

        po.setTaskStatus(MeasurementStatusEnum.TASKSTATUS_CLOSED.getCode());

        measurementTaskDAO.save(po);
        // 保存日志
        measurementTaskRecordService.add(beforePO, po, MeasurementTaskRecordConst.CLOSE, loginUser.getId(), loginUser.getBelong().getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reOpen(MeasurementTaskModifyFO input, LoginUser loginUser) {
        MeasurementTask po = measurementTaskDAO.findById(input.getId()).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        MeasurementTask beforePO = new MeasurementTask();
        BeanUtil.copyProperties(po, beforePO);

        po.setTaskStatus(MeasurementStatusEnum.TASKSTATUS_UNCLAIMED.getCode());

        measurementTaskDAO.save(po);
        // 保存日志
        measurementTaskRecordService.add(beforePO, po, MeasurementTaskRecordConst.REOPEN, loginUser.getId(), loginUser.getBelong().getCode());
    }

    /**
     * 测量任务操作日志
     *
     * @param taskId
     * @param status
     * @param loginUser
     */
    @Override
    public void measureOperationUpdateEnvPrepareStatus(String taskId, String status, LoginUser loginUser) {
        MeasurementTask po = measurementTaskDAO.findById(taskId).orElseThrow(() -> new BusinessAssertException("未查询到测量任务"));
        MeasurementTask beforePO = new MeasurementTask();
        BeanUtil.copyProperties(po, beforePO);

        po.setEnvPrepareStatus(status);

        measurementTaskDAO.save(po);
        // 保存日志
        measurementTaskRecordService.add(beforePO, po, MeasurementStatusEnum.get(status).getDesc(), loginUser.getId(), loginUser.getBelong().getCode());
        measurementTaskDAO.flush();
    }


    /**
     * 生成测量任务id
     *
     * @return
     */
    private String getTaskId() {
        String dateStr = DateUtil.format(LocalDate.now(), DatePattern.PURE_DATE_PATTERN);
        String task_seq = NumberUtil.decimalFormat("0000", redisHelper.strIncrement(RedisCacheKeys.getMeasurementTaskSeqKey(dateStr), 1L));

        StringBuilder sb = new StringBuilder("CL-");
        sb.append(dateStr);
        sb.append(task_seq);
        return sb.toString();
    }
}
