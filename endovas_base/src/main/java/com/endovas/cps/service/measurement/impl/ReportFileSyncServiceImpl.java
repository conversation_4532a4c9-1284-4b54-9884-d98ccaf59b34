package com.endovas.cps.service.measurement.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.endovas.cps.dao.measurement.MeasurementTaskDAO;
import com.endovas.cps.dao.measurement.MeasurementTaskResultDAO;
import com.endovas.cps.dao.platform.EquipmentDAO;
import com.endovas.cps.entity.measurement.MeasurementTask;
import com.endovas.cps.entity.platform.Equipment;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.enums.EquipmentStatusEnum;
import com.endovas.cps.enums.MeasureSourceTypeEnum;
import com.endovas.cps.pojo.dto.AttachmentUploadFileDTO;
import com.endovas.cps.pojo.fo.measurement.MeasurementResultAddFO;
import com.endovas.cps.pojo.fo.measurement.filesync.QueryReportFileExistFO;
import com.endovas.cps.pojo.vo.AttachmentVO;
import com.endovas.cps.pojo.vo.measurement.filesync.QueryReportFileExistVO;
import com.endovas.cps.service.attachment.AttachmentService;
import com.endovas.cps.service.measurement.MeasurementResultService;
import com.endovas.cps.service.measurement.ReportFileSyncService;
import io.daige.starter.common.exception.BusinessAssertException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wk
 * @Date: 2024/11/26
 * @Time: 15:40
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReportFileSyncServiceImpl implements ReportFileSyncService {

    private final EquipmentDAO equipmentDAO;
    private final MeasurementTaskResultDAO measurementTaskResultDAO;
    private final AttachmentService attachmentService;
    private final MeasurementResultService measurementResultService;
    private final MeasurementTaskDAO measurementTaskDAO;

    @Override
    public List<String> getEquipUserNameList(String hostname,String protocol) {
        List<String> equipList = equipmentDAO.findByHostnameAndProtocolAndStatus(hostname,protocol, EquipmentStatusEnum.INUSE.getCode()).stream().map(Equipment::getUsername).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(equipList)){
            return new ArrayList<>();
        }
        return equipList;
    }

    @Override
    public QueryReportFileExistVO reportFileExist(QueryReportFileExistFO param) {
        MeasurementTask taskPO = measurementTaskDAO.findByMeasurementTaskId(param.getMeasurementTaskId());
        if (Objects.isNull(taskPO)) {
            throw new BusinessAssertException("未查询到测量任务信息");
        }
        int count = measurementTaskResultDAO.countByMeasurementIdAndResultFileHashAndSourceType(taskPO.getId(), param.getFileHash(), MeasureSourceTypeEnum.UPLOAD.getCode());
        Boolean isExist = count > 0 ? Boolean.TRUE : Boolean.FALSE;

        QueryReportFileExistVO vo = new QueryReportFileExistVO();
        vo.setIsExist(isExist);
        vo.setFileHash(param.getFileHash());
        vo.setMeasurementTaskId(param.getMeasurementTaskId());
        return vo;
    }

    @Override
    public void uploadReportFile(MultipartFile file, String measurementTaskId, String fileHash) {
        // 上传文件
        AttachmentUploadFileDTO attachmentUploadFileDTO = new AttachmentUploadFileDTO();
        attachmentUploadFileDTO.setAttachmentType(AttachmentTypeEnum.MEASUREMENT_RESULT.getCode());
        attachmentUploadFileDTO.setContentType(file.getContentType());
        attachmentUploadFileDTO.setBody(file);
        AttachmentVO attVO = attachmentService.uploadFile(attachmentUploadFileDTO);

        MeasurementTask taskPO = measurementTaskDAO.findByMeasurementTaskId(measurementTaskId);
        if (Objects.isNull(taskPO)) {
            throw new BusinessAssertException("未查询到测量任务信息");
        }

        // 新建文件
        MeasurementResultAddFO fo = new MeasurementResultAddFO();
        fo.setAttachmentId(attVO.getId());
        fo.setMeasurementId(taskPO.getId());
        fo.setFileHash(fileHash);
        measurementResultService.upload(fo, null);
    }
}
