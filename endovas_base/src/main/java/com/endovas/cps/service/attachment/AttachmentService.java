package com.endovas.cps.service.attachment;

import com.aliyun.oss.model.OSSObject;
import com.endovas.cps.entity.attachment.Attachment;
import com.endovas.cps.enums.AttachmentTypeEnum;
import com.endovas.cps.pojo.dto.AttachmentLocalFileDTO;
import com.endovas.cps.pojo.dto.AttachmentUploadFileDTO;
import com.endovas.cps.pojo.vo.AttachmentVO;

import java.io.IOException;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: bin.yu
 * Date: 2021/6/17
 * Time: 上午11:19
 */
public interface AttachmentService {
    //文件上传
    AttachmentVO uploadFile(AttachmentUploadFileDTO attachmentUploadFileDTO);

    //服务器内部生成文件并存储
    AttachmentVO saveFile(AttachmentLocalFileDTO dto) throws IOException;
    void saveFile(Attachment attachment) ;

    //逻辑删除
    void deleteFile(String id);

    /**
     * 物理删除
     *
     * @param id 必须是attachment表的id
     */
    void physicalDeleteFile(String id);

    OSSObject viewFile(String filePath) throws IOException;

    void updateTmpUrlById(String existAttachmentTempId, String targetId, AttachmentTypeEnum type);

    void updateTmpUrlById(List<String> existAttachmentTempIds, String targetId, AttachmentTypeEnum type);


    List<AttachmentVO> findByTargetIdAndType(String targetId, AttachmentTypeEnum type);

    AttachmentVO getByTargetIdAndType(String targetId, AttachmentTypeEnum type);


}
