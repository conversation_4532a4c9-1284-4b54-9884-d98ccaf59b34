package com.endovas.cps.enums;

import cn.hutool.core.util.ObjectUtil;
import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum MeasureSourceTypeEnum implements BaseEnum {
    UPLOAD("本地上传"),
    LOCAL("线上编辑"),

    ;

    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static MeasureSourceTypeEnum match(String val, MeasureSourceTypeEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static MeasureSourceTypeEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(MeasureSourceTypeEnum val) {
        return ObjectUtil.isNotNull(val) && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }
}
