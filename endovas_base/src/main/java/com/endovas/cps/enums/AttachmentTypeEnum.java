package com.endovas.cps.enums;

import io.daige.starter.common.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AttachmentTypeEnum implements BaseEnum {
    HOSPITAL_LOGO("医院Logo"),//hospitalId
    SOFT_UPGRADE_PACKAGE("应用程序升级包"),//versionId
    CATH_ROOM("导管室"),//cathRoomId;
    TRACK("随访"), //patientTrackId
    NORMAL_FILE("普通影像文件"), //normalFileId,

    // 使用阿里oss的filekey作为下载地址
    DICOM("医疗影像文件"), //dicomId
    DICOM_PICTURE("医疗影像文件截图"),//dicomId

    MEASUREMENT_RESULT("测量报告"), //measurementResultId
    MEASUREMENT_RESULT_PIC("测量报告里的图"),//measurementResultId
    CBC("血常规"),//patientId
    UA("尿常规"),//patientId
    BMP("生化全套"),//patientId
    ABG("血气分析"),//patientId
    IS("传染病筛查"),//patientId
    CK("心肌酶及心肌标志物"),//patientId
    MB("肌红蛋白"), //patientId
    COAG("凝血功能"),//patientId
    D_DIMER("D-二聚体"),//patientId
    CRP("C-反应蛋白"), //patientId
    ;
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static AttachmentTypeEnum match(String val, AttachmentTypeEnum def) {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static AttachmentTypeEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(AttachmentTypeEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    public String getCode() {
        return this.name();
    }

}